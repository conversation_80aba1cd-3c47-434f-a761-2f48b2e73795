import {Empty, Form, Typography} from 'antd';
import {MCPParamsDynamicFormItem} from './MCPParamsDynamicFormItem';
import {useMCPToolDebugContext} from './Providers/MCPToolDebugProvider';

export function MCPToolParamsForm() {
    const {
        toolDebugFormInstance,
        toolParams,
    } = useMCPToolDebugContext();

    return (
        <Form form={toolDebugFormInstance} className="mcp-tool-params-form">
            {
                toolParams.length > 0 ? toolParams.map(item => (
                    <MCPParamsDynamicFormItem
                        key={item.name}
                        name={item.name}
                        label={item.name}
                        description={item.description}
                        type={item.dataType}
                        required={item.required}
                        example={item.example}
                    />
                )) : <Empty description={<Typography.Text>暂无参数</Typography.Text>} />
            }
        </Form>
    );
}

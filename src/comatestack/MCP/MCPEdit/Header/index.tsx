import {Flex, Segmented, TabsProps} from 'antd';
import styled from '@emotion/styled';
import {Button} from '@panda-design/components';
import EllipsisButton from '@/components/MCP/EllipsisButton';
import {MCPEditTab} from '@/types/mcp/mcp';
import PublishButton from '../PublishButton';
import MoveButton from '../MoveButton';
import DeleteButton from '../DeleteButton';
import RepealButton from '../RepealButton';
import ActionButtons from '../ActionButtons';
import BaseInfo from './BaseInfo';

const Wrapper = styled.div`
    position: relative;
    margin-top: 16px;
`;

const StyledSegmented = styled(Segmented)`
    &.ant-5-segmented {
        .ant-segmented-item {
            padding: 5px 12px;
            border: 1px solid #fff;
            height: 38px;
            display: flex;
            align-items: center;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
        }
        .ant-5-segmented-thumb {
            height: 38px;
            min-height: 38px;
            box-shadow: inset 0 0 0 1px #0080ff;
            background: #fff;
            transition: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
        }
        .ant-5-segmented-item-selected {
            border: 1px solid #0080ff;
            background: #fff;
            height: 38px;
            .ant-5-segmented-item-label {
                color: #0080ff;
            }
        }
    }
`;

const items = [
    {
        key: MCPEditTab.ServerInfo,
        label: '基本信息',
    },
    {
        key: MCPEditTab.Tools,
        label: '工具配置',
    },
    {
        key: MCPEditTab.Analysis,
        label: '数据分析',
    },
];

type Props = TabsProps;
export default function Header({activeKey, onChange}: Props) {

    return (
        <Wrapper>
            <BaseInfo style={{maxWidth: 'calc(50% - 160px)'}} />
            <Flex justify="center" align="center" style={{minHeight: 38}}>
                <StyledSegmented
                    options={items.map(item => ({
                        label: item.label,
                        value: item.key,
                    }))}
                    value={activeKey}
                    onChange={onChange as (value: unknown) => void}
                    size="large"
                />
            </Flex>
            <Flex
                align="center"
                gap={8}
                style={{position: 'absolute', right: 24, top: 0, height: '100%'}}
            >
                <RepealButton />
                <ActionButtons activeTab={activeKey as MCPEditTab} />
                <PublishButton />
                <EllipsisButton>
                    <Button
                        type="text"
                        disabled
                        tooltip="即将上线"
                    >
                        历史
                    </Button>
                    <MoveButton />
                    <DeleteButton />
                </EllipsisButton>
            </Flex>
        </Wrapper>

    );
}

import {Flex} from 'antd';
import {useCallback, memo} from 'react';
import {useMCPServerId, useMCPWorkspaceId} from '@/components/MCP/hooks';
import {loadMCPServer, useMCPServer} from '@/regions/mcp/mcpServer';
import {MCPSpaceLink, MCPSquareLink} from '@/links/mcp';
import {MCPHeader, MCPActions, MCPDescription, MCPDetails} from './components';

const MCPInfo = memo(() => {
    const mcpServerId = useMCPServerId();
    const mcpServer = useMCPServer(mcpServerId);
    const workspaceId = useMCPWorkspaceId();

    const refresh = useCallback(
        () => {
            loadMCPServer({mcpServerId});
        },
        [mcpServerId]
    );

    const backUrl = workspaceId
        ? MCPSpaceLink.toUrl({workspaceId})
        : MCPSquareLink.toUrl();

    return (
        <Flex vertical gap={16}>
            <Flex gap={14} align="center" justify="space-between">
                <MCPHeader server={mcpServer} backUrl={backUrl} />
                <MCPActions
                    server={mcpServer}
                    serverId={mcpServerId}
                    refresh={refresh}
                />
            </Flex>
            <MCPDescription description={mcpServer?.description} />
            <MCPDetails server={mcpServer} />
        </Flex>
    );
});

MCPInfo.displayName = 'MCPInfo';

export default MCPInfo;

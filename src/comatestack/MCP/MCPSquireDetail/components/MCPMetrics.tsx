import {Flex, Tooltip} from 'antd';
import {memo, ReactNode} from 'react';
import {formatCount} from '@/utils/mcp/format';
import {IconCallCount, IconEye} from '@/icons/mcp';
import {MCPServerMetrics} from '@/types/mcp/mcp';

interface MetricItemProps {
    icon: ReactNode;
    value: number;
    tooltip: string;
}

const MetricItem = memo(({icon, value, tooltip}: MetricItemProps) => (
    <Tooltip title={tooltip}>
        <Flex align="center" gap={4}>
            {icon}
            {formatCount(value)}
        </Flex>
    </Tooltip>
));

MetricItem.displayName = 'MetricItem';

interface MCPMetricsProps {
    metrics?: MCPServerMetrics;
}

const MCPMetrics = memo(({metrics}: MCPMetricsProps) => {
    return (
        <Flex align="center" gap={12} style={{color: '#545454'}}>
            <MetricItem
                icon={<IconEye />}
                value={metrics?.viewCount ?? 0}
                tooltip="浏览量"
            />
            <MetricItem
                icon={<IconCallCount />}
                value={metrics?.callCount ?? 0}
                tooltip="调用量"
            />
        </Flex>
    );
});

MCPMetrics.displayName = 'MCPMetrics';

export default MCPMetrics;

import {Flex, Space, Divider} from 'antd';
import {memo} from 'react';
import {MCPCollectButton} from '@/components/MCP/MCPCollectButton';
import {MCPSubscribeButton} from '@/components/MCP/MCPSubscribeButton';
import {MCPServerBase} from '@/types/mcp/mcp';
import MCPMetrics from './MCPMetrics';

interface MCPActionsProps {
    server?: MCPServerBase;
    serverId: number;
    refresh: () => void;
}

const MCPActions = memo(({server, serverId, refresh}: MCPActionsProps) => {
    return (
        <Flex>
            <Space split={<Divider type="vertical" style={{borderColor: '#D9D9D9'}} />}>
                <MCPMetrics metrics={server?.serverMetrics} />
                <MCPCollectButton
                    refresh={refresh}
                    favorite={server?.favorite ?? false}
                    serverId={serverId}
                />
                <MCPSubscribeButton
                    id={serverId}
                    workspaceId={server?.workspaceId}
                    showText
                />
            </Space>
        </Flex>
    );
});

MCPActions.displayName = 'MCPActions';

export default MCPActions;

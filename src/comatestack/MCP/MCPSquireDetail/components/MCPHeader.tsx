import {Flex, Typography, Divider} from 'antd';
import {memo} from 'react';
import MCPServerAvatar from '@/components/MCP/MCPServerAvatar';
import {getServerTypeText} from '@/components/MCP/MCPServerTypeTag';
import PublishInfo from '@/components/MCP/PublishInfo';
import GoBackButton from '@/components/MCP/GoBackButton';
import {MCPServerBase} from '@/types/mcp/mcp';

interface MCPHeaderProps {
    server?: MCPServerBase;
    backUrl: string;
}

const MCPHeader = memo(({server, backUrl}: MCPHeaderProps) => {
    return (
        <Flex gap={14} align="center" justify="space-between">
            <Flex align="center" gap={16}>
                <GoBackButton size={24} url={backUrl} />
                <MCPServerAvatar size={40} icon={server?.icon} radius={4} />
                <Flex align="flex-start" vertical>
                    <Typography.Title level={4} style={{fontSize: 16, color: '#181818'}}>
                        {server?.name}
                    </Typography.Title>
                    <Flex align="center" style={{fontSize: 12, color: '#8F8F8F'}}>
                        {getServerTypeText(server?.serverSourceType)}
                        <Divider type="vertical" style={{borderColor: '#D9D9D9', margin: '0 12px'}} />
                        {server?.serverProtocolType}
                        <Divider type="vertical" style={{borderColor: '#D9D9D9', margin: '0 12px'}} />
                        <PublishInfo
                            username={server?.lastModifyUser}
                            time={server?.lastModifyTime}
                        />
                    </Flex>
                </Flex>
            </Flex>
        </Flex>
    );
});

MCPHeader.displayName = 'MCPHeader';

export default MCPHeader;

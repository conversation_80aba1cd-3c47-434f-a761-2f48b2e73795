import {Tooltip} from 'antd';
import styled from '@emotion/styled';
import {useRef, useEffect, useState, memo} from 'react';

const DescriptionWrapper = styled.div`
    background-color: #F8F8F8;
    padding: 10px 12px;
    color: #3C3C3C;
    position: relative;
`;

const DescriptionContainer = styled.div`
    font-size: 14px;
    line-height: 22px;
    position: relative;
    max-height: 66px;
    overflow: hidden;
`;

const DescriptionText = styled.div`
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    text-overflow: ellipsis;
    word-break: break-word;
`;

interface MCPDescriptionProps {
    description?: string;
}

const MCPDescription = memo(({description}: MCPDescriptionProps) => {
    const descriptionRef = useRef<HTMLDivElement>(null);
    const [isOverflowing, setIsOverflowing] = useState(false);

    useEffect(
        () => {
            const checkOverflow = () => {
                if (descriptionRef.current) {
                    const element = descriptionRef.current;
                    const isOverflowingNow = element.scrollHeight > element.clientHeight;
                    setIsOverflowing(isOverflowingNow);
                }
            };

            checkOverflow();
            window.addEventListener('resize', checkOverflow);
            return () => window.removeEventListener('resize', checkOverflow);
        },
        [description]
    );

    const displayText = description || '暂无描述';

    return (
        <DescriptionWrapper>
            {description && isOverflowing ? (
                <Tooltip title={description} placement="top">
                    <DescriptionContainer ref={descriptionRef}>
                        <DescriptionText>{displayText}</DescriptionText>
                    </DescriptionContainer>
                </Tooltip>
            ) : (
                <DescriptionContainer ref={descriptionRef}>
                    <DescriptionText>{displayText}</DescriptionText>
                </DescriptionContainer>
            )}
        </DescriptionWrapper>
    );
});

MCPDescription.displayName = 'MCPDescription';

export default MCPDescription;

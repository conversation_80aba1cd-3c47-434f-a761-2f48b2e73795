import styled from '@emotion/styled';
import {DEVELOP_ZONE_ID, LeftBg, OPERATION_ZONE_ID, RightBg} from './constant';

const operationBg = 'linear-gradient(rgba(238,238,255, 0.5), rgba(241,244,251, 0))';

const developBg = 'linear-gradient(rgba(238,249,255, 0.5), rgba(240,249,255, 0))';

const testBg = 'linear-gradient(rgba(251,243,255, 0.3), rgba(255, 255, 255, 0))';

const LeftBgContainer = ({zoneId}: {zoneId?: string}) => {
    const src = zoneId ? LeftBg[zoneId] : '';
    return (
        <div style={{
            position: 'absolute',
            left: 0,
            top: 0,
            zIndex: 0,
        }}
        >
            <img
                src={src}
                alt=""
            />
        </div>
    );
};

const RightBgContainer = ({zoneId}: {zoneId?: string}) => {
    const src = zoneId ? RightBg[zoneId] : '';
    return (
        <div style={{
            position: 'absolute',
            right: 0,
            top: 0,
            zIndex: 0,
        }}
        >
            <img
                src={src}
                alt=""
            />
        </div>
    );
};

const TopBgContainer = styled.div<{zoneId: string, height: number}>`
    position: absolute;
    top: 0px;
    left: 0px;
    width: 100%;
    height: ${props => props.height}px;
    background: ${
    props => (
        props.zoneId === DEVELOP_ZONE_ID
            ? developBg
            : props.zoneId === OPERATION_ZONE_ID ? operationBg : testBg
    )};
`;

interface Props {
    zoneId?: string;
    height: number;
}

export const HeaderBackground = ({zoneId, height}: Props) => {
    return (
        <TopBgContainer zoneId={zoneId} height={height}>
            <LeftBgContainer zoneId={zoneId} />
            <RightBgContainer zoneId={zoneId} />
        </TopBgContainer>
    );
};
